#!/usr/bin/env python3

import subprocess
import glob
import re

def extract_strings_from_frame(frame_path):
    """Extract strings from a PNG frame, excluding PNG headers"""
    try:
        result = subprocess.run(['strings', frame_path], capture_output=True, text=True)
        lines = result.stdout.strip().split('\n')
        
        # Filter out PNG-specific strings
        filtered_lines = []
        for line in lines:
            if line not in ['IHDR', 'IDAT', 'IDATx', 'IEND', ''] and not line.startswith('IDAT'):
                filtered_lines.append(line)
        
        return filtered_lines
    except Exception as e:
        print(f"Error processing {frame_path}: {e}")
        return []

def main():
    frame_files = sorted(glob.glob('extracted_pngs/frame_*.png'))
    
    print("Extracting strings from all frames:")
    print("=" * 50)
    
    all_strings = []
    frame_data = {}
    
    for i, frame_path in enumerate(frame_files):
        frame_num = i
        strings = extract_strings_from_frame(frame_path)
        frame_data[frame_num] = strings
        
        print(f"Frame {frame_num:03d}: {strings}")
        
        # Collect all strings for analysis
        all_strings.extend(strings)
    
    print("\n" + "=" * 50)
    print("ANALYSIS:")
    
    # Look for flag-like patterns
    flag_chars = []
    potential_flag_parts = []
    
    for frame_num in sorted(frame_data.keys()):
        strings = frame_data[frame_num]
        for s in strings:
            # Look for characters that might be part of a flag
            if any(c in s for c in '{}flag_'):
                potential_flag_parts.append((frame_num, s))
                print(f"Frame {frame_num:03d} - Potential flag part: '{s}'")
    
    # Try to extract first character from each frame
    print(f"\nFirst characters from each frame:")
    first_chars = ""
    for frame_num in sorted(frame_data.keys()):
        strings = frame_data[frame_num]
        if strings and strings[0]:
            first_char = strings[0][0]
            first_chars += first_char
            print(f"Frame {frame_num:03d}: '{first_char}' (from '{strings[0]}')")
    
    print(f"\nConcatenated first characters: '{first_chars}'")
    
    # Try to extract all characters in sequence
    print(f"\nAll strings concatenated:")
    all_concat = ''.join(all_strings)
    print(f"'{all_concat[:200]}...'")
    
    # Look for flag pattern in concatenated string
    flag_matches = re.findall(r'[a-zA-Z0-9_]*{[^}]*}[a-zA-Z0-9_]*', all_concat)
    if flag_matches:
        print(f"\nPotential flags found: {flag_matches}")

if __name__ == '__main__':
    main()
