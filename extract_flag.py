#!/usr/bin/env python3

import os
import glob
from PIL import Image
import numpy as np

def extract_lsb_from_image(image_path, channel=0, num_bits=1):
    """Extract LSB data from an image"""
    try:
        img = Image.open(image_path)
        img_array = np.array(img)
        
        # Extract the specified channel (0=R, 1=G, 2=B)
        if len(img_array.shape) == 3:
            channel_data = img_array[:, :, channel]
        else:
            channel_data = img_array
        
        # Extract LSBs
        lsb_data = []
        for row in channel_data:
            for pixel in row:
                # Extract the specified number of least significant bits
                for bit in range(num_bits):
                    lsb_data.append((pixel >> bit) & 1)
        
        return lsb_data
    except Exception as e:
        print(f"Error processing {image_path}: {e}")
        return []

def bits_to_bytes(bits):
    """Convert a list of bits to bytes"""
    bytes_data = []
    for i in range(0, len(bits), 8):
        if i + 7 < len(bits):
            byte_bits = bits[i:i+8]
            byte_value = 0
            for j, bit in enumerate(byte_bits):
                byte_value |= (bit << j)  # LSB first
            bytes_data.append(byte_value)
    return bytes(bytes_data)

def extract_printable_strings(data, min_length=4):
    """Extract printable strings from binary data"""
    strings = []
    current_string = ""
    
    for byte in data:
        if 32 <= byte <= 126:  # Printable ASCII
            current_string += chr(byte)
        else:
            if len(current_string) >= min_length:
                strings.append(current_string)
            current_string = ""
    
    if len(current_string) >= min_length:
        strings.append(current_string)
    
    return strings

def main():
    frame_files = sorted(glob.glob('extracted_pngs/frame_*.png'))
    print(f"Processing {len(frame_files)} frames...")
    
    # Try different channels and bit combinations
    for channel in range(3):  # R, G, B
        channel_name = ['R', 'G', 'B'][channel]
        print(f"\n=== Analyzing {channel_name} channel ===")
        
        for num_bits in [1, 2]:
            print(f"\nExtracting {num_bits} LSB(s) from {channel_name} channel:")
            
            all_bits = []
            for frame_path in frame_files:
                bits = extract_lsb_from_image(frame_path, channel, num_bits)
                all_bits.extend(bits)
            
            if all_bits:
                # Convert to bytes
                data = bits_to_bytes(all_bits)
                
                # Look for printable strings
                strings = extract_printable_strings(data, min_length=8)
                
                if strings:
                    print(f"  Found {len(strings)} strings:")
                    for s in strings[:10]:  # Show first 10 strings
                        print(f"    {s}")
                
                # Look for flag patterns
                data_str = data.decode('latin-1', errors='ignore')
                if 'flag' in data_str.lower() or 'ctf' in data_str.lower() or '{' in data_str:
                    print(f"  Potential flag data found!")
                    print(f"  Data preview: {data_str[:200]}")
                
                # Save raw data for manual inspection
                output_file = f"extracted_data_{channel_name}_{num_bits}bit.bin"
                with open(output_file, 'wb') as f:
                    f.write(data)
                print(f"  Saved raw data to {output_file}")

    # Try combining data from all frames in sequence
    print(f"\n=== Trying sequential combination ===")
    combined_data = b""
    
    for frame_path in frame_files:
        # Try extracting 1 byte per frame from different positions
        for channel in range(3):
            bits = extract_lsb_from_image(frame_path, channel, 1)
            if len(bits) >= 8:
                byte_data = bits_to_bytes(bits[:8])  # Take first byte
                combined_data += byte_data
    
    # Look for flag in combined data
    combined_str = combined_data.decode('latin-1', errors='ignore')
    if 'flag' in combined_str.lower() or '{' in combined_str:
        print("Potential flag found in combined data!")
        print(f"Combined data: {combined_str[:500]}")
    
    with open("combined_sequential.bin", 'wb') as f:
        f.write(combined_data)
    print("Saved combined data to combined_sequential.bin")

if __name__ == '__main__':
    main()
