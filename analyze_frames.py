#!/usr/bin/env python3

import os
import subprocess
import glob

def analyze_frame_with_zsteg(frame_path):
    """Analyze a frame with zsteg and return any interesting findings"""
    try:
        result = subprocess.run(['zsteg', frame_path], 
                              capture_output=True, text=True, timeout=30)
        
        lines = result.stdout.split('\n')
        interesting_lines = []
        
        for line in lines:
            # Look for lines that don't end with ".." (which indicates no data)
            if line.strip() and not line.strip().endswith('..'):
                # Skip file type detections that are likely false positives
                if 'file:' not in line or any(keyword in line.lower() for keyword in ['text', 'ascii', 'flag', 'ctf']):
                    interesting_lines.append(line.strip())
        
        return interesting_lines
    except Exception as e:
        return [f"Error analyzing {frame_path}: {e}"]

def extract_lsb_data(frame_path, channel='b', bit=1):
    """Extract LSB data from a specific channel"""
    try:
        cmd = ['zsteg', frame_path, f'b{bit},{channel},lsb,xy']
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=30)
        return result.stdout.strip()
    except Exception as e:
        return f"Error: {e}"

def main():
    frame_files = sorted(glob.glob('extracted_pngs/frame_*.png'))
    
    print(f"Analyzing {len(frame_files)} frames...")
    
    all_findings = []
    
    for i, frame_path in enumerate(frame_files):
        print(f"\nAnalyzing {frame_path}...")
        
        # Try zsteg analysis
        findings = analyze_frame_with_zsteg(frame_path)
        
        if findings:
            print(f"  Interesting findings in {frame_path}:")
            for finding in findings:
                print(f"    {finding}")
                all_findings.append((frame_path, finding))
        
        # Try extracting LSB data from different channels
        for channel in ['r', 'g', 'b']:
            for bit in [1, 2]:
                lsb_data = extract_lsb_data(frame_path, channel, bit)
                if lsb_data and len(lsb_data) > 10:  # Only consider substantial data
                    # Check if it contains printable characters
                    printable_chars = sum(1 for c in lsb_data if c.isprintable())
                    if printable_chars > len(lsb_data) * 0.7:  # At least 70% printable
                        print(f"  LSB data in {channel} channel, bit {bit}: {lsb_data[:100]}...")
                        all_findings.append((frame_path, f"LSB {channel}{bit}: {lsb_data}"))
    
    # Summary
    print(f"\n\nSUMMARY: Found {len(all_findings)} interesting findings")
    for frame_path, finding in all_findings:
        print(f"{frame_path}: {finding}")

if __name__ == '__main__':
    main()
