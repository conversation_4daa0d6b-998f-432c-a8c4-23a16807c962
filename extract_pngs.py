#!/usr/bin/env python3

import struct
import os

# PNG offsets from binwalk output
png_offsets = [
    1550, 3432, 5282, 7192, 9097, 10976, 12884, 14791, 16667, 18589,
    20505, 22422, 24285, 26220, 28156, 30065, 32002, 33944, 35848, 37803,
    39753, 41709, 43643, 45580, 47563, 49533, 51510, 53484, 55452, 57436,
    59436, 61393, 63325, 65304, 67255, 69214, 71177, 73150, 75112, 77098,
    79087, 81027, 82928, 84894
]

def extract_png_files():
    with open('memory_dump.bin', 'rb') as f:
        data = f.read()
    
    os.makedirs('extracted_pngs', exist_ok=True)
    
    for i, offset in enumerate(png_offsets):
        # Find the end of the PNG file by looking for IEND chunk
        start = offset
        pos = start
        
        # Look for PNG signature first
        if data[pos:pos+8] != b'\x89PNG\r\n\x1a\n':
            print(f"Warning: PNG signature not found at offset {offset}")
            continue
            
        # Find IEND chunk to determine file end
        pos = start + 8  # Skip PNG signature
        while pos < len(data) - 12:
            # Read chunk length and type
            if pos + 8 > len(data):
                break
            chunk_length = struct.unpack('>I', data[pos:pos+4])[0]
            chunk_type = data[pos+4:pos+8]
            
            if chunk_type == b'IEND':
                # Found end of PNG, include the IEND chunk (length + type + CRC = 12 bytes)
                end_pos = pos + 8 + 4  # length field + type field + CRC
                break
            
            # Move to next chunk
            pos += 8 + chunk_length + 4  # length + type + data + CRC
        else:
            # If we didn't find IEND, try to estimate based on next PNG or end of file
            if i + 1 < len(png_offsets):
                end_pos = png_offsets[i + 1]
            else:
                end_pos = len(data)
        
        # Extract PNG data
        png_data = data[start:end_pos]
        
        # Save to file
        filename = f'extracted_pngs/frame_{i:03d}.png'
        with open(filename, 'wb') as png_file:
            png_file.write(png_data)
        
        print(f"Extracted {filename} (offset: {offset}, size: {len(png_data)} bytes)")

if __name__ == '__main__':
    extract_png_files()
